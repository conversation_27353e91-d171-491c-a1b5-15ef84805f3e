# Roles Lifecycle Diagram

%% This diagram illustrates the lifecycle and interactions between the Reader, Writer, and Analyst roles
%% throughout the knowledge extraction and application process for "Bel Canto"

```mermaid
gantt
    title Roles Lifecycle for Knowledge Extraction and Application
    dateFormat  YYYY-MM-DD
    axisFormat %d
    
    section Course Timeline
    Module 1 (Bel Canto)            :2023-01-01, 21d
    Quiz #1                         :milestone, 2023-01-21, 0d
    Discussion Post #1              :milestone, 2023-01-23, 0d
    
    section Reader Role
    Initial Reading                 :r1, 2023-01-01, 7d
    Mark Important Passages         :r2, after r1, 3d
    Extract Quotes                  :r3, after r2, 2d
    Identify Literary Elements      :r4, after r2, 2d
    Track Characters                :r5, after r2, 2d
    Note Themes                     :r6, after r2, 2d
    Organize Knowledge Base         :r7, after r3 r4 r5 r6, 3d
    
    section Analyst Role
    Review Knowledge Base           :a1, after r7, 2d
    Analyze Linguistic Patterns     :a2, after a1, 3d
    Create Linguistic Templates     :a3, after a2, 2d
    Identify Knowledge Gaps         :a4, after a1, 1d
    Recommend Additional Extractions:a5, after a4, 1d
    Update Knowledge Base           :a6, after a5, 2d
    Review Discussion Draft         :a7, 2023-01-18, 2d
    Provide Writing Feedback        :a8, after a7, 1d
    
    section Writer Role
    Review Knowledge Base           :w1, after a6, 1d
    Prepare for Discussion          :w2, after w1, 2d
    Draft Discussion Post           :w3, after w2, 3d
    Incorporate Feedback            :w4, after a8, 1d
    Submit Discussion Post          :w5, after w4, 0d
    Prepare for Quiz                :w6, 2023-01-19, 2d
```

## Roles Lifecycle Explanation

This Gantt chart illustrates how the three roles (Reader, Writer, and Analyst) interact over time throughout the knowledge extraction and application process for "Bel Canto." The lifecycle shows the temporal relationships, dependencies, and handoffs between roles.

### Phase 1: Initial Knowledge Extraction (Reader-dominated)

1. **Initial Reading**: The Reader begins by reading "Bel Canto" and marking important passages
2. **Knowledge Extraction**: The Reader extracts various types of knowledge (quotes, literary elements, characters, themes)
3. **Knowledge Organization**: The Reader organizes the extracted knowledge into the knowledge base

### Phase 2: Analysis and Enhancement (Analyst-dominated)

1. **Knowledge Review**: The Analyst reviews the knowledge base for completeness and accuracy
2. **Linguistic Analysis**: The Analyst identifies linguistic patterns and creates templates
3. **Gap Identification**: The Analyst identifies gaps in the knowledge base
4. **Knowledge Enhancement**: The Analyst recommends additional extractions to fill gaps
5. **Knowledge Update**: The Reader updates the knowledge base based on Analyst recommendations

### Phase 3: Content Creation (Writer-dominated)

1. **Knowledge Application**: The Writer reviews the enhanced knowledge base
2. **Content Preparation**: The Writer prepares for discussion posts and quizzes
3. **Content Creation**: The Writer drafts discussion posts using the knowledge base
4. **Content Review**: The Analyst reviews the draft and provides feedback
5. **Content Refinement**: The Writer incorporates feedback and finalizes content
6. **Submission**: The Writer submits the discussion post and takes the quiz

### Continuous Improvement Cycle

Throughout the lifecycle, there is a continuous feedback loop:
1. Reader extracts knowledge
2. Analyst reviews and enhances knowledge
3. Writer applies knowledge to create content
4. Analyst reviews content and provides feedback
5. Writer refines content based on feedback
6. Process repeats for subsequent assignments

## Alternative Lifecycle Visualization

%% This diagram shows the cyclical nature of the roles' interactions

```mermaid
graph TD
    %% Main cycle
    A[Start: New Assignment] --> B[Reader: Extract Knowledge]
    B --> C[Analyst: Review & Enhance Knowledge]
    C --> D[Writer: Create Content]
    D --> E[Analyst: Review Content]
    E --> F[Writer: Refine Content]
    F --> G[Submit Assignment]
    G --> H[Feedback & Grades]
    H --> I[Analyst: Process Feedback]
    I --> J[Next Assignment]
    J --> B
    
    %% Knowledge base interactions
    B --> K[Knowledge Base]
    C --> K
    K --> D
    
    %% Linguistic pattern database
    C --> L[Linguistic Pattern Database]
    L --> D
    L --> E
    
    %% Styling
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style G fill:#d8f3dc,stroke:#333,stroke-width:2px
    style K fill:#ade8f4,stroke:#333,stroke-width:2px
    style L fill:#ade8f4,stroke:#333,stroke-width:2px
```

## Role Transitions and Handoffs

The lifecycle illustrates several critical transitions and handoffs between roles:

1. **Reader to Analyst**: After the Reader extracts knowledge, the Analyst reviews it for quality and completeness
2. **Analyst to Reader**: The Analyst identifies gaps and recommends additional extractions for the Reader
3. **Analyst to Writer**: The Analyst provides linguistic patterns and enhanced knowledge for the Writer
4. **Writer to Analyst**: The Writer submits draft content for the Analyst to review
5. **Analyst to Writer**: The Analyst provides feedback for the Writer to incorporate

These transitions ensure that each role contributes its specialized expertise to the overall process, resulting in high-quality knowledge extraction and application.

## Timing Considerations

The lifecycle is designed to ensure that:

1. Knowledge extraction is completed well before content creation deadlines
2. The Analyst has sufficient time to review both knowledge and content
3. The Writer has time to incorporate feedback before submission deadlines
4. Quiz preparation occurs in parallel with discussion post creation
5. The process allows for continuous improvement based on feedback

By following this lifecycle, the three roles work together efficiently to support all course requirements while maintaining high quality standards.
