# Analyst Role Workflow

%% This diagram illustrates the workflow for the Analyst role
%% The Analyst reviews both the knowledge base and written outputs
%% to provide feedback and improvements, including linguistic analysis

```mermaid
graph TD
    %% Starting points - reviewing knowledge and writing
    A[Review Knowledge Base] --> B[Analyze Extraction Quality]
    A --> C[Analyze Writing Outputs]
    A --> D[Analyze Linguistic Patterns]

    %% Knowledge base analysis
    B --> E1[Identify Knowledge Gaps]
    B --> E2[Evaluate Citation Accuracy]
    B --> E3[Assess Thematic Coverage]

    %% Knowledge base improvement
    E1 --> F1[Recommend Additional Extractions]
    E2 --> F2[Correct Citation Formats]
    E3 --> F3[Suggest Thematic Reorganization]

    F1 --> G[Update Knowledge Base]
    F2 --> G
    F3 --> G

    %% Linguistic analysis
    D --> H1[Identify Effective Sentence Structures]
    D --> H2[Catalog Rhetorical Devices]
    D --> H3[Extract Stylistic Elements]
    D --> H4[Analyze Paragraph Construction]

    %% Dictionary integration
    H1 --> I1[Create Sentence Pattern Library]
    H2 --> I2[Build Rhetorical Device Examples]
    H3 --> I3[Develop Stylistic Templates]
    H4 --> I4[Document Paragraph Structures]

    I1 --> J[Linguistic Pattern Database]
    I2 --> J
    I3 --> J
    I4 --> J

    %% Dictionary resource utilization
    J --> K[Consult Dictionary Resources]
    K --> L[Vocabulary Enhancement Suggestions]

    %% Writing analysis
    C --> M1[Evaluate Discussion Posts]
    C --> M2[Evaluate Peer Responses]
    C --> M3[Evaluate Research Paper]

    %% Discussion post analysis
    M1 --> N1[Assess Argument Strength]
    M1 --> N2[Check Evidence Usage]
    M1 --> N3[Review MLA Formatting]
    M1 --> N4[Analyze Sentence Variety]

    %% Feedback generation
    N1 --> O1[Provide Feedback on Arguments]
    N2 --> O2[Suggest Additional Evidence]
    N3 --> O3[Correct Citation Errors]
    N4 --> O4[Recommend Linguistic Improvements]

    J --> O4
    L --> O4

    O1 --> P[Deliver Feedback to Writer]
    O2 --> P
    O3 --> P
    O4 --> P

    %% Continuous improvement
    G --> Q[Continuous Improvement of Knowledge Base]
    P --> R[Continuous Improvement of Writing]

    %% Styling for better visualization
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style G fill:#ade8f4,stroke:#333,stroke-width:2px
    style J fill:#d8f3dc,stroke:#333,stroke-width:2px
    style Q fill:#ade8f4,stroke:#333,stroke-width:2px
    style R fill:#ade8f4,stroke:#333,stroke-width:2px
```

## Analyst Role Responsibilities

The Analyst role focuses on evaluating and improving both the knowledge extraction and writing processes:

1. **Knowledge Base Analysis**: Identifying gaps, checking citation accuracy, and assessing thematic coverage

2. **Linguistic Pattern Analysis**:
   - Identifying effective sentence structures and rhetorical devices in "Bel Canto"
   - Creating a library of stylistic elements that can be repurposed in academic writing
   - Analyzing how Patchett constructs paragraphs to create rhythm and emphasis

3. **Vocabulary Enhancement**:
   - Utilizing dictionary resources from `utils/dictionary.links`
   - Suggesting more precise or elevated vocabulary alternatives
   - Identifying patterns of word choice that create specific effects

4. **Writing Evaluation**:
   - Reviewing arguments, evidence usage, and MLA formatting
   - Analyzing sentence variety and paragraph structure
   - Suggesting linguistic improvements based on patterns found in the novel

5. **Feedback Provision**: Offering specific suggestions for improvement to both Reader and Writer

6. **Continuous Improvement**: Ensuring the knowledge base and writing quality improve over time

The Analyst serves as a critical bridge between the Reader and Writer roles, ensuring both the content quality and linguistic sophistication of the entire knowledge extraction and application process.
