# Writer Role Workflow

%% This diagram illustrates the workflow for the Writer role
%% The Writer uses the knowledge base to create discussion posts,
%% peer responses, and the research paper

```mermaid
graph TD
    %% Starting point - accessing the knowledge base
    A[Access Knowledge Base] --> B[Review Extracted Knowledge]
    
    %% Different writing tasks
    B --> C1[Prepare for Discussion Posts]
    B --> C2[Prepare for Peer Responses]
    B --> C3[Prepare for Research Paper]
    
    %% Discussion post preparation
    C1 --> D1[Select Relevant Quotes & Evidence]
    C1 --> D2[Identify Key Themes to Discuss]
    C1 --> D3[Formulate Thesis Statement]
    
    %% Discussion post writing process
    D1 --> E1[Draft Discussion Post]
    D2 --> E1
    D3 --> E1
    
    E1 --> F1[Incorporate MLA Citations]
    F1 --> G1[Review & Edit]
    G1 --> H1[Submit Discussion Post]
    
    %% Peer response process
    C2 --> I1[Read Peer Posts]
    I1 --> I2[Identify Points to Expand Upon]
    I2 --> I3[Draft Peer Response]
    I3 --> I4[Submit Peer Response]
    
    %% Research paper process
    C3 --> J1[Compare Novels]
    C3 --> J2[Research Secondary Sources]
    J1 --> J3[Develop Comparative Thesis]
    J2 --> J3
    J3 --> J4[Draft Research Paper]
    J4 --> J5[Incorporate Citations]
    J5 --> J6[Review & Edit]
    J6 --> J7[Submit Research Paper]
    
    %% Styling for better visualization
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style H1 fill:#d8f3dc,stroke:#333,stroke-width:2px
    style I4 fill:#d8f3dc,stroke:#333,stroke-width:2px
    style J7 fill:#d8f3dc,stroke:#333,stroke-width:2px
```

## Writer Role Responsibilities

The Writer role focuses on using the extracted knowledge to create academic content:

1. **Discussion Posts**: Creating 700-1000 word essays with proper MLA citations and textual evidence
2. **Peer Responses**: Writing 150-250 word commentaries on peers' discussion posts
3. **Research Paper**: Developing a 2,000-2,500 word comparative analysis with secondary sources

The Writer relies on the knowledge base created by the Reader and receives feedback from the Analyst to improve their work.
