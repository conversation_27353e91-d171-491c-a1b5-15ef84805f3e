# Reader Role Workflow

%% This diagram illustrates the workflow for the Reader role
%% The Reader is responsible for extracting knowledge from the text
%% and organizing it in the knowledge base for later use

```mermaid
graph TD
    %% Main reading and extraction flow
    A[Start Reading] --> B[Initial Reading Phase]
    B --> C[Mark Important Passages]
    C --> D[Note Initial Reactions]
    D --> E[Structured Extraction Phase]
    
    %% Different types of knowledge extraction
    E --> F1[Extract Quotes]
    E --> F2[Identify Literary Elements]
    E --> F3[Track Characters]
    E --> F4[Note Themes]
    E --> F5[Record Context]
    E --> F6[Analyze Language]
    
    %% Adding to knowledge base
    F1 --> G[Add to Knowledge Base]
    F2 --> G
    F3 --> G
    F4 --> G
    F5 --> G
    F6 --> G
    
    %% Refining and organizing extracted knowledge
    G --> H[Review & Refine Extractions]
    H --> I[Organize by Theme/Character/Element]
    I --> J[Add Page Numbers for Citation]
    J --> K[Complete Knowledge Extraction]
    
    %% Applications of extracted knowledge
    K --> L1[Support Quiz Preparation]
    K --> L2[Support Discussion Writing]
    K --> L3[Support Research Paper]
    
    %% Styling for better visualization
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style K fill:#ade8f4,stroke:#333,stroke-width:2px
    style L1 fill:#d8f3dc,stroke:#333,stroke-width:2px
    style L2 fill:#d8f3dc,stroke:#333,stroke-width:2px
    style L3 fill:#d8f3dc,stroke:#333,stroke-width:2px
```

## Reader Role Responsibilities

The Reader role is focused on the systematic extraction of knowledge from the text. This involves:

1. **Initial Reading**: Comprehending the text and marking important passages
2. **Structured Extraction**: Categorizing information into different types (quotes, themes, characters, etc.)
3. **Organization**: Adding proper citations and organizing by themes or literary elements
4. **Application**: Ensuring the extracted knowledge supports course requirements

The Reader creates the foundation for both the Writer and Analyst roles by building a comprehensive knowledge base.
