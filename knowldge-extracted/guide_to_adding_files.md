# Guide to Adding Files to the Knowledge Extraction Structure

This guide explains how to add new files to the knowledge extraction structure for "Bel Canto" by <PERSON>. Following these guidelines will ensure consistency and organization across all extracted knowledge.

## Directory Structure

```
knowldge-extracted/
├── README.md                  # Main documentation and guidelines
├── guide_to_adding_files.md   # This guide
├── quotes.txt                 # Direct quotations from the text
├── literary_elements.txt      # Analysis of narrative structure, setting, symbolism, etc.
├── characters.txt             # Character analysis and development
├── themes.txt                 # Themes and motifs
├── context.txt                # Historical, cultural, and literary context
├── language.txt               # Analysis of language and style
├── questions.txt              # Critical questions for discussion and analysis
├── reflections.txt            # Personal reflections and connections
└── chapters/                  # Chapter-specific content
    ├── chapter1/              # Content specific to Chapter 1
    │   └── summary.md         # Summary of Chapter 1
    ├── chapter2/              # Content specific to Chapter 2
    │   └── summary.md         # Summary of Chapter 2
    └── ...                    # Additional chapter directories
```

## File Types and Their Purpose

### Main Knowledge Files

These files contain extracted knowledge organized by category across the entire novel:

1. **quotes.txt**: Direct passages from the text that provide evidence for literary analysis
   - Include page numbers for proper MLA citation
   - Categorize by theme, character, or literary device
   - Format: "Quote" (<PERSON><PERSON> page#)

2. **literary_elements.txt**: Analysis of narrative techniques and structural elements
   - Include narrative structure, setting, symbolism, imagery, etc.
   - Provide specific examples with page references
   - Format: Element: Description (page#)

3. **characters.txt**: Information about characters and their development
   - Include main and supporting characters
   - Track character development and relationships
   - Format: Character Name: Trait/Development (page#)

4. **themes.txt**: Analysis of themes and motifs
   - Identify major and minor themes
   - Track how themes develop throughout the novel
   - Format: Theme: Evidence/Development (page#)

5. **context.txt**: Background information relevant to understanding the novel
   - Include historical, cultural, and literary context
   - Format: Context: Information (page#)

6. **language.txt**: Analysis of language and stylistic techniques
   - Note patterns in language use, figurative language, etc.
   - Format: Technique/Word: Example (page#)

7. **questions.txt**: Critical questions for discussion and analysis
   - Include interpretive, analytical, and comparative questions
   - Format: Question (related page#)

8. **reflections.txt**: Personal responses and connections
   - Include initial reactions, connections to other texts, evolving interpretations
   - Format: Reflection: Thought (page#)

### Chapter-Specific Files

Each chapter has its own directory with files specific to that chapter:

1. **summary.md**: A concise summary of the chapter's events, characters, and themes
   - Include plot summary, key events, significant passages, character introductions, setting, themes introduced, and narrative techniques

## How to Add New Files

### Adding Knowledge from a New Chapter

1. **Create a Chapter Directory**:
   ```bash
   mkdir -p knowldge-extracted/chapters/chapter[X]
   ```
   Replace `[X]` with the chapter number.

2. **Create a Chapter Summary**:
   ```bash
   touch knowldge-extracted/chapters/chapter[X]/summary.md
   ```
   Fill in the summary following the template in chapter1/summary.md.

3. **Update Main Knowledge Files**:
   - Open each main knowledge file (quotes.txt, literary_elements.txt, etc.)
   - Add a new section for the chapter:
     ```
     ## Chapter [X]
     ```
   - Add extracted knowledge under this heading, following the established format

### Adding a New Category of Knowledge

1. **Create the New File**:
   ```bash
   touch knowldge-extracted/[new_category].txt
   ```
   Replace `[new_category]` with the name of your new category.

2. **Structure the File**:
   - Begin with a title and description:
     ```
     # [Category Name] in Bel Canto by Ann Patchett
     ```
   - Add chapter sections:
     ```
     ## Chapter 1
     
     [Content for Chapter 1]
     
     ## Chapter 2
     
     [Content for Chapter 2]
     ```

3. **Update README.md**:
   - Add your new category to the "Categories of Knowledge to Extract" section

## Formatting Guidelines

1. **Use Markdown Formatting**:
   - `#` for main titles
   - `##` for chapter headings
   - `###` for subcategories
   - `- ` for bullet points
   - `**Bold**` for emphasis

2. **Include Page Numbers**:
   - Always include page numbers for citations: (p. X) or (p. X-Y)
   - For quotes, use the format: "Quote" (Patchett page#)

3. **Organize by Chapter**:
   - Keep content organized by chapter for easy reference
   - Use consistent headings across all files

4. **Be Consistent**:
   - Follow the established formats for each file type
   - Maintain consistent terminology and style

## Example: Adding Content from Chapter 2

1. Create the chapter directory:
   ```bash
   mkdir -p knowldge-extracted/chapters/chapter2
   ```

2. Create the chapter summary:
   ```bash
   touch knowldge-extracted/chapters/chapter2/summary.md
   ```

3. Add content to the summary following the template.

4. Update each main knowledge file with Chapter 2 content:
   - Open quotes.txt and add:
     ```
     ## Chapter 2
     
     ### [Theme or Category]
     "Quote from Chapter 2" (Patchett page#)
     ```
   - Repeat for other knowledge files

## Tips for Effective Knowledge Extraction

1. **Read Actively**:
   - Mark passages of interest while reading
   - Note initial reactions and questions

2. **Extract Systematically**:
   - Re-read marked sections and extract content into appropriate categories
   - Use the cleaned text file with page numbers for accurate citation

3. **Analyze Patterns**:
   - Look for connections between extracted elements
   - Connect to course concepts about the novel form

4. **Focus on Quality**:
   - Prioritize significant insights over quantity
   - Include specific textual evidence for all claims

5. **Consider Application**:
   - Extract knowledge with discussions, quizzes, and the research paper in mind
   - Note particularly strong evidence for potential arguments

By following these guidelines, you'll contribute to a comprehensive and well-organized knowledge base that will support all course requirements for "Bel Canto."
