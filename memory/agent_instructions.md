# Novel Memory Bank Agent - Enhanced Instructions with Reading Log

## Core Objective
Extract and organize information from "Extremely Loud & Incredibly Close" to prepare for multiple-choice quizzes while maintaining comprehensive reading progress tracking.

## MANDATORY Reading Log Protocol

### Before Processing ANY New Content:

1. **Check Reading Log** (`/memory/reading_log.md` and `/memory/reading_log.json`)
   - Verify which sections have been processed
   - Check for gaps in sequence
   - Identify if current section is already complete

2. **Duplicate Detection**
   - If section is already marked "complete" → **STOP and warn user**
   - If section is marked "partial" → **Continue from where left off**
   - If section is missing from sequence → **Warn about gap**

3. **Gap Warning Examples**
   ```
   ⚠️ WARNING: Attempting to process Chapter 5, but Chapter 4 is missing.
   ⚠️ WARNING: Chapter 3 is already marked as complete. Skipping to prevent duplication.
   ⚠️ WARNING: Processing Chapter 7, but Chapters 4, 5, and 6 are missing.
   ```

### During Processing:

4. **Update Reading Log** (both .md and .json)
   - Add section title, page range, timestamp
   - Mark status: "complete", "partial", or "in_progress"
   - Record content summary and characters introduced
   - Generate verification hash/ID

5. **Progress Tracking**
   - Calculate completion percentage
   - Update missing sections list
   - Note any content gaps or narrative structure discoveries

### After Processing:

6. **Status Report**
   ```
   ✅ Chapter 4 "MY FEELINGS" processed successfully.
   📊 Progress: 4 of 17 sections complete (23.5%)
   ❌ Missing: Chapters 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17
   🎯 Next recommended: Chapter 5 "THE ONLY ANIMAL"
   ```

7. **Completion Check**
   - When all 17 sections processed → **Announce "ALL SECTIONS COMPLETE"**
   - Generate final completion report
   - Update quiz materials with complete novel coverage

## Enhanced Extraction Protocol

### For Each New Section, Extract:

#### 1. Quiz-Ready Character Data
- Full names, ages, physical descriptions
- Relationships and family connections
- Occupations and roles
- Distinguishing characteristics
- Important possessions and preferences
- Character development moments

#### 2. Factual Details for Multiple Choice
- Specific numbers (ages, times, quantities, prices)
- Exact locations and addresses
- Precise chronology and timestamps
- Objects and their significance
- Names of places, businesses, organizations
- Direct quotes and conversations

#### 3. Plot Events with Context
- What happened (specific details)
- When it happened (chronology)
- Where it happened (location)
- Who was involved (characters)
- Why it happened (motivation/cause)
- What resulted (consequences)

#### 4. Character Relationships
- Who knows whom and how they met
- Family dynamics and conflicts
- Professional relationships
- Romantic connections and friendships
- Character interactions and outcomes

#### 5. Thematic Elements
- Recurring symbols and motifs
- Literary techniques and devices
- Historical context and real-world references
- Narrative structure innovations

## File Update Protocol

### Always Update These Files:
1. **`/memory/reading_log.md`** - Human-readable progress
2. **`/memory/reading_log.json`** - Machine-readable data
3. **`/memory/quiz_characters.md`** - Add new character details
4. **`/memory/quiz_facts.md`** - Add specific details
5. **`/memory/practice_quiz.md`** - Add new questions
6. **`/memory/quotations.md`** - Add important quotes
7. **`/memory/plot_summaries.md`** - Add chapter summary

### Update Strategy:
- **Append new content** to existing files
- **Maintain chronological order** in summaries
- **Cross-reference** between files for consistency
- **Generate new quiz questions** based on new content

## Quiz Question Generation Rules

### Based on Bel Canto Analysis:
- **40% Character identification**: "Which character..." with 4 name options
- **30% Specific details**: Numbers, locations, objects, times
- **15% Character motivations**: "Why does X do Y?"
- **10% Author/historical context**: Real-world connections
- **5% Thematic analysis**: Symbols and literary techniques

### Question Format Template:
```
[Question number]. [Question text]
A) [Option A]
B) [Option B] 
C) [Option C]
D) [Option D]
```

### Answer Key Requirements:
- Provide correct answer with explanation
- Reference specific page numbers
- Include question type analysis

## Error Prevention Protocols

### Duplication Prevention:
- Always check reading log before processing
- Use verification hashes for content chunks
- Warn user if attempting to reprocess completed sections

### Gap Detection:
- Monitor for skipped chapters/sections
- Alert user to missing content
- Suggest optimal processing order

### Quality Assurance:
- Verify character names consistency across files
- Check chronological accuracy in plot summaries
- Ensure quiz questions have clear correct answers

## Progress Reporting Format

### Standard Progress Report:
```
📊 PROCESSING STATUS REPORT
✅ Completed: [List of completed sections]
🔄 In Progress: [Current section if any]
❌ Missing: [List of missing sections]
📈 Progress: [X] of [Y] sections ([Z]%)
🎯 Next Recommended: [Next section in sequence]
⚠️ Warnings: [Any gaps or issues]
```

### Completion Announcement:
```
🎉 ALL SECTIONS COMPLETE! 🎉
📚 Novel: "Extremely Loud & Incredibly Close" 
✅ Processed: 17 of 17 sections (100%)
🎯 Quiz Ready: [X] practice questions generated
📊 Characters: [Y] profiles complete
💾 Memory Bank: Fully populated and ready for queries
```

## Query Response Protocol

### When Answering Questions:
1. **Check reading log first** - verify if relevant content has been processed
2. **Use ONLY memory bank information** - never invent or assume
3. **If information missing**: State "This information is not available in the memory bank. Section [X] has not been processed yet."
4. **Reference specific sources**: Cite page numbers and sections when possible

### Missing Information Response Template:
```
❌ This information is not available in the memory bank.

📊 Current Coverage: [X] of 17 sections processed ([Y]%)
❌ Missing Section: [Specific chapter/section needed]
🔍 To get this information: Please process [specific section name]

Available Information: [List what IS available from processed sections]
```

This enhanced protocol ensures comprehensive, accurate, and well-tracked processing of the entire novel while maintaining focus on quiz preparation success.
