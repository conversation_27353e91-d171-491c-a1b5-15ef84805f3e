#!/usr/bin/env python3
import sys
import os
import PyPDF2
import re
from typing import List, Optional

# Try to import OCR libraries, but make them optional
try:
    import fitz  # PyMuPDF
    from PIL import Image
    import io
    try:
        from pytesseract import image_to_string
        OCR_AVAILABLE = True
    except ImportError:
        OCR_AVAILABLE = False
except ImportError:
    OCR_AVAILABLE = False

class PDFExtractor:
    """
    PDF content extractor that supports both regular PDFs and scanned documents.
    Inspired by the approach in the MCP PDF extraction server.
    """

    def __init__(self):
        pass

    def clean_pdf_text(self, input_text):
        """
        Clean text extracted from PDF by:
        1. Removing single spaces between letters
        2. Reducing double spaces between words to single spaces

        Args:
            input_text (str): The text to clean

        Returns:
            str: The cleaned text
        """
        # Step 1: Remove spaces between single letters
        # This pattern looks for a letter, followed by a space, followed by another letter
        # and removes the space between them
        pattern1 = r'(?<=[a-zA-Z]) (?=[a-zA-Z])'
        text = re.sub(pattern1, '', input_text)

        # Step 2: Replace double spaces with single spaces
        # Keep repeating until no more double spaces are found
        while '  ' in text:
            text = text.replace('  ', ' ')

        # Step 3: Fix common punctuation issues
        # Fix spaces before punctuation
        text = re.sub(r' ([.,;:!?)])', r'\1', text)
        # Fix spaces after opening parentheses
        text = re.sub(r'([(]) ', r'\1', text)

        return text

    def is_scanned_pdf(self, pdf_path: str) -> bool:
        """
        Check if the PDF is a scanned document (image-based) or a normal text-based PDF.

        Args:
            pdf_path (str): Path to the PDF file

        Returns:
            bool: True if the PDF is a scanned document, False otherwise
        """
        reader = PyPDF2.PdfReader(pdf_path)
        # Check the first few pages to see if they contain text
        pages_to_check = min(5, len(reader.pages))
        for i in range(pages_to_check):
            if reader.pages[i].extract_text().strip():
                return False
        return True

    def parse_pages(self, pages_str: Optional[str], total_pages: int) -> List[int]:
        """
        Parse a page selection string into a list of page numbers.

        Args:
            pages_str (str): String representing page numbers (e.g., "1,3,5-7,-1")
            total_pages (int): Total number of pages in the PDF

        Returns:
            List[int]: List of page numbers to extract
        """
        if not pages_str:
            return list(range(total_pages))

        pages = []
        for part in pages_str.split(','):
            part = part.strip()
            if not part:
                continue

            # Handle page ranges (e.g., "5-10")
            if '-' in part and part.count('-') == 1 and part[0] != '-':
                try:
                    start, end = part.split('-')
                    start = int(start.strip())
                    end = int(end.strip())

                    # Convert to 0-based indexing
                    start = start - 1 if start > 0 else start
                    end = end - 1 if end > 0 else end

                    # Handle negative indices
                    if start < 0:
                        start = total_pages + start
                    if end < 0:
                        end = total_pages + end

                    # Ensure valid range
                    start = max(0, min(start, total_pages - 1))
                    end = max(0, min(end, total_pages - 1))

                    # Add range to pages list
                    pages.extend(range(start, end + 1))
                except ValueError:
                    continue
            else:
                # Handle single page numbers
                try:
                    page_num = int(part)

                    # Handle negative indices (e.g., -1 for last page)
                    if page_num < 0:
                        page_num = total_pages + page_num
                    else:
                        # Convert to 0-based indexing
                        page_num = page_num - 1

                    # Ensure valid page number
                    if 0 <= page_num < total_pages:
                        pages.append(page_num)
                except ValueError:
                    continue

        # Remove duplicates and sort
        return sorted(set(pages))

    def extract_text_from_normal(self, pdf_path: str, pages: List[int]) -> str:
        """
        Extract text from a normal (text-based) PDF.

        Args:
            pdf_path (str): Path to the PDF file
            pages (List[int]): List of page numbers to extract

        Returns:
            str: Extracted text with page numbers
        """
        reader = PyPDF2.PdfReader(pdf_path)
        extracted_text = []

        for page_num in pages:
            page = reader.pages[page_num]
            text = page.extract_text()
            extracted_text.append(f"\n\n--- Page {page_num + 1} ---\n\n{text}")

            # Print progress
            if (page_num + 1) % 10 == 0:
                print(f"Processed {page_num + 1} pages...")

        return "".join(extracted_text)

    def extract_text_from_scanned(self, pdf_path: str, pages: List[int]) -> str:
        """
        Extract text from a scanned (image-based) PDF using OCR.

        Args:
            pdf_path (str): Path to the PDF file
            pages (List[int]): List of page numbers to extract

        Returns:
            str: Extracted text with page numbers
        """
        if not OCR_AVAILABLE:
            raise ImportError("OCR libraries (PyMuPDF, PIL, pytesseract) are required for scanned PDFs")

        doc = fitz.open(pdf_path)
        extracted_text = []

        for page_num in pages:
            page = doc.load_page(page_num)
            pix = page.get_pixmap()
            img = Image.open(io.BytesIO(pix.tobytes()))

            # Use OCR to extract text (default to English)
            text = image_to_string(img, lang='eng')
            extracted_text.append(f"\n\n--- Page {page_num + 1} ---\n\n{text}")

            # Print progress
            if (page_num + 1) % 10 == 0:
                print(f"Processed {page_num + 1} pages...")

        return "".join(extracted_text)

    def extract_content(self, pdf_path: str, pages_str: Optional[str] = None, output_path: Optional[str] = None, clean_text: bool = False) -> str:
        """
        Extract content from a PDF file.

        Args:
            pdf_path (str): Path to the PDF file
            pages_str (str, optional): String representing page numbers to extract
            output_path (str, optional): Path to save the extracted text
            clean_text (bool, optional): Whether to clean the extracted text

        Returns:
            str: Extracted text with page numbers
        """
        try:
            # Check if the PDF file exists
            if not os.path.exists(pdf_path):
                print(f"Error: PDF file '{pdf_path}' does not exist.")
                return ""

            # Get total pages
            reader = PyPDF2.PdfReader(pdf_path)
            total_pages = len(reader.pages)
            print(f"Total pages: {total_pages}")

            # Parse page selection
            selected_pages = self.parse_pages(pages_str, total_pages)

            # Check if PDF is scanned
            is_scanned = self.is_scanned_pdf(pdf_path)

            # Extract text based on PDF type
            if is_scanned and OCR_AVAILABLE:
                print("Detected scanned PDF, using OCR...")
                text = self.extract_text_from_scanned(pdf_path, selected_pages)
            else:
                if is_scanned and not OCR_AVAILABLE:
                    print("Detected scanned PDF, but OCR libraries are not available. Falling back to normal extraction...")
                text = self.extract_text_from_normal(pdf_path, selected_pages)

            # Clean the text if requested
            if clean_text:
                print("Cleaning extracted text...")
                text = self.clean_pdf_text(text)

            # Save to file if output path is provided
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as output_file:
                    output_file.write(text)
                print(f"Text extraction completed. Saved to '{output_path}'")

            return text

        except Exception as e:
            print(f"Error extracting text from PDF: {str(e)}")
            return ""

if __name__ == "__main__":
    import argparse

    # Create argument parser
    parser = argparse.ArgumentParser(description='Extract text from a PDF file.')
    parser.add_argument('pdf_path', help='Path to the PDF file')
    parser.add_argument('output_path', help='Path to save the extracted text')
    parser.add_argument('--pages', help='Page numbers to extract (e.g., "1,3,5-7,-1")')
    parser.add_argument('--clean', action='store_true',
                        help='Clean the extracted text by removing spaces between letters and fixing formatting')

    # Parse arguments
    args = parser.parse_args()

    # Create PDF extractor
    extractor = PDFExtractor()

    # Extract text from the PDF
    text = extractor.extract_content(args.pdf_path, args.pages, args.output_path, args.clean)

    # Exit with appropriate status code
    sys.exit(0 if text else 1)
